{"name": "@tap2go/shared-auth", "version": "0.1.0", "description": "Shared Firebase authentication package for Tap2Go monorepo", "main": "src/index.ts", "types": "src/index.ts", "scripts": {"build": "tsc", "dev": "tsc --watch", "clean": "rm -rf dist", "type-check": "tsc --noEmit"}, "dependencies": {"firebase": "^11.8.1", "@tap2go/firebase-config": "workspace:*", "@tap2go/database": "workspace:*", "@tap2go/shared-types": "workspace:*", "react": "^19.0.0"}, "devDependencies": {"@types/react": "^19.0.14", "@types/node": "^20.19.2", "typescript": "^5.8.3"}, "peerDependencies": {"react": "^19.0.0"}, "exports": {".": {"types": "./src/index.ts", "default": "./src/index.ts"}, "./contexts": {"types": "./src/contexts/index.ts", "default": "./src/contexts/index.ts"}, "./hooks": {"types": "./src/hooks/index.ts", "default": "./src/hooks/index.ts"}, "./services": {"types": "./src/services/index.ts", "default": "./src/services/index.ts"}, "./types": {"types": "./src/types/index.ts", "default": "./src/types/index.ts"}}}