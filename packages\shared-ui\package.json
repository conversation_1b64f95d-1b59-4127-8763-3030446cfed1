{"name": "shared-ui", "version": "0.1.0", "private": true, "main": "./src/index.ts", "types": "./src/index.ts", "scripts": {"build": "tsc", "dev": "tsc --watch", "lint": "eslint src/", "type-check": "tsc --noEmit", "clean": "if exist dist rmdir /s /q dist"}, "dependencies": {"react": "^19.0.0", "react-dom": "^19.0.0", "lucide-react": "^0.511.0", "@heroicons/react": "^2.2.0", "clsx": "^2.1.1", "tailwindcss": "^4", "shared-types": "workspace:*", "typescript": "^5.8.3"}, "devDependencies": {"@types/react": "^19", "@types/react-dom": "^19"}, "peerDependencies": {"react": "^19.0.0", "react-dom": "^19.0.0"}}