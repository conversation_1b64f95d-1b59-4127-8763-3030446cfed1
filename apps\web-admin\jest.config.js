/**
 * Jest Configuration for web-admin
 */

const nextJest = require('next/jest');

const createJestConfig = nextJest({
  // Provide the path to your Next.js app to load next.config.js and .env files
  dir: './',
});

// Add any custom config to be passed to Jest
const customJestConfig = {
  displayName: 'web-admin',
  setupFilesAfterEnv: ['<rootDir>/../../jest.setup.js'],
  
  testMatch: [
    '<rootDir>/src/**/__tests__/**/*.{ts,tsx}',
    '<rootDir>/src/**/*.{test,spec}.{ts,tsx}',
  ],
  
  collectCoverageFrom: [
    'src/**/*.{ts,tsx}',
    '!src/**/*.d.ts',
    '!src/**/*.stories.{ts,tsx}',
    '!src/pages/_app.tsx',
    '!src/pages/_document.tsx',
  ],
  
  moduleNameMapping: {
    '^@tap2go/shared-auth$': '<rootDir>/../../packages/shared-auth/src',
    '^@tap2go/shared-types$': '<rootDir>/../../packages/shared-types/src',
    '^@tap2go/shared-ui$': '<rootDir>/../../packages/shared-ui/src',
    '^@tap2go/firebase-config$': '<rootDir>/../../packages/firebase-config/src',
    '^@tap2go/database$': '<rootDir>/../../packages/database/src',
  },
  
  testEnvironment: 'jsdom',
  
  testPathIgnorePatterns: [
    '<rootDir>/node_modules/',
    '<rootDir>/.next/',
  ],
};

// createJestConfig is exported this way to ensure that next/jest can load the Next.js config which is async
module.exports = createJestConfig(customJestConfig);
