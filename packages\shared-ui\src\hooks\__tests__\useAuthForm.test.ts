/**
 * useAuthForm Hook Tests
 * Tests for the shared authentication form hook
 */

import { renderHook, act } from '@testing-library/react';
import { useAuthForm, type AuthFormConfig } from '../useAuthForm';

describe('useAuthForm', () => {
  const mockFormConfig: AuthFormConfig = {
    fields: [
      {
        name: 'email',
        label: 'Email',
        type: 'email',
        placeholder: 'Enter email',
        validation: {
          required: true,
          email: true,
        },
      },
      {
        name: 'password',
        label: 'Password',
        type: 'password',
        placeholder: 'Enter password',
        validation: {
          required: true,
          password: {
            minLength: 8,
            requireUppercase: true,
            requireLowercase: true,
            requireNumber: true,
          },
        },
      },
    ],
    submitButtonText: 'Submit',
    loadingText: 'Submitting...',
  };

  it('should initialize with empty form data', () => {
    const { result } = renderHook(() => useAuthForm(mockFormConfig));

    expect(result.current.formData).toEqual({
      email: '',
      password: '',
    });
    expect(result.current.errors).toEqual({});
    expect(result.current.isSubmitting).toBe(false);
  });

  it('should handle input changes', () => {
    const { result } = renderHook(() => useAuthForm(mockFormConfig));

    act(() => {
      result.current.handleInputChange({
        target: { name: 'email', value: '<EMAIL>' },
      } as React.ChangeEvent<HTMLInputElement>);
    });

    expect(result.current.formData.email).toBe('<EMAIL>');
  });

  it('should validate required fields', () => {
    const { result } = renderHook(() => useAuthForm(mockFormConfig));

    act(() => {
      const isValid = result.current.validateForm();
      expect(isValid).toBe(false);
    });

    expect(result.current.errors.email).toBeDefined();
    expect(result.current.errors.password).toBeDefined();
  });

  it('should validate email format', () => {
    const { result } = renderHook(() => useAuthForm(mockFormConfig));

    act(() => {
      result.current.handleInputChange({
        target: { name: 'email', value: 'invalid-email' },
      } as React.ChangeEvent<HTMLInputElement>);
    });

    act(() => {
      const isValid = result.current.validateForm();
      expect(isValid).toBe(false);
    });

    expect(result.current.errors.email).toContain('valid email');
  });

  it('should validate password requirements', () => {
    const { result } = renderHook(() => useAuthForm(mockFormConfig));

    act(() => {
      result.current.handleInputChange({
        target: { name: 'password', value: 'weak' },
      } as React.ChangeEvent<HTMLInputElement>);
    });

    act(() => {
      const isValid = result.current.validateForm();
      expect(isValid).toBe(false);
    });

    expect(result.current.errors.password).toBeDefined();
  });

  it('should pass validation with valid data', () => {
    const { result } = renderHook(() => useAuthForm(mockFormConfig));

    act(() => {
      result.current.handleInputChange({
        target: { name: 'email', value: '<EMAIL>' },
      } as React.ChangeEvent<HTMLInputElement>);
    });

    act(() => {
      result.current.handleInputChange({
        target: { name: 'password', value: 'StrongPass123!' },
      } as React.ChangeEvent<HTMLInputElement>);
    });

    act(() => {
      const isValid = result.current.validateForm();
      expect(isValid).toBe(true);
    });

    expect(Object.keys(result.current.errors)).toHaveLength(0);
  });

  it('should handle form submission', async () => {
    const mockOnSubmit = jest.fn().mockResolvedValue(undefined);
    const { result } = renderHook(() => useAuthForm(mockFormConfig));

    // Set valid form data
    act(() => {
      result.current.handleInputChange({
        target: { name: 'email', value: '<EMAIL>' },
      } as React.ChangeEvent<HTMLInputElement>);
    });

    act(() => {
      result.current.handleInputChange({
        target: { name: 'password', value: 'StrongPass123!' },
      } as React.ChangeEvent<HTMLInputElement>);
    });

    // Submit form
    await act(async () => {
      await result.current.handleSubmit(
        { preventDefault: jest.fn() } as any,
        mockOnSubmit
      );
    });

    expect(mockOnSubmit).toHaveBeenCalledWith({
      email: '<EMAIL>',
      password: 'StrongPass123!',
    });
  });

  it('should handle submission errors', async () => {
    const mockOnSubmit = jest.fn().mockRejectedValue(new Error('Submission failed'));
    const { result } = renderHook(() => useAuthForm(mockFormConfig));

    // Set valid form data
    act(() => {
      result.current.handleInputChange({
        target: { name: 'email', value: '<EMAIL>' },
      } as React.ChangeEvent<HTMLInputElement>);
    });

    act(() => {
      result.current.handleInputChange({
        target: { name: 'password', value: 'StrongPass123!' },
      } as React.ChangeEvent<HTMLInputElement>);
    });

    // Submit form
    await act(async () => {
      await result.current.handleSubmit(
        { preventDefault: jest.fn() } as any,
        mockOnSubmit
      );
    });

    expect(result.current.isSubmitting).toBe(false);
  });

  it('should clear field errors', () => {
    const { result } = renderHook(() => useAuthForm(mockFormConfig));

    act(() => {
      result.current.setFieldError('email', 'Test error');
    });

    expect(result.current.errors.email).toBe('Test error');

    act(() => {
      result.current.clearFieldError('email');
    });

    expect(result.current.errors.email).toBeUndefined();
  });

  it('should clear all errors', () => {
    const { result } = renderHook(() => useAuthForm(mockFormConfig));

    act(() => {
      result.current.setFieldError('email', 'Email error');
      result.current.setFieldError('password', 'Password error');
    });

    expect(Object.keys(result.current.errors)).toHaveLength(2);

    act(() => {
      result.current.clearErrors();
    });

    expect(Object.keys(result.current.errors)).toHaveLength(0);
  });
});
