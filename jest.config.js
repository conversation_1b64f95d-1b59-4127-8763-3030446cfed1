/**
 * Jest Configuration for Tap2Go Monorepo
 * Comprehensive testing setup for all packages and apps
 */

module.exports = {
  // Use projects for monorepo testing
  projects: [
    // Shared packages
    '<rootDir>/packages/shared-types/jest.config.js',
    '<rootDir>/packages/shared-ui/jest.config.js',
    '<rootDir>/packages/shared-auth/jest.config.js',
    '<rootDir>/packages/shared-utils/jest.config.js',
    '<rootDir>/packages/database/jest.config.js',
    '<rootDir>/packages/firebase-config/jest.config.js',
    '<rootDir>/packages/api-client/jest.config.js',
    '<rootDir>/packages/business-logic/jest.config.js',
    '<rootDir>/packages/config/jest.config.js',
    
    // Apps
    '<rootDir>/apps/web/jest.config.js',
    '<rootDir>/apps/web-admin/jest.config.js',
    '<rootDir>/apps/web-driver/jest.config.js',
    '<rootDir>/apps/web-vendor/jest.config.js',
  ],

  // Global test configuration
  collectCoverageFrom: [
    'packages/*/src/**/*.{ts,tsx}',
    'apps/*/src/**/*.{ts,tsx}',
    '!**/*.d.ts',
    '!**/*.stories.{ts,tsx}',
    '!**/*.test.{ts,tsx}',
    '!**/*.spec.{ts,tsx}',
    '!**/node_modules/**',
    '!**/dist/**',
    '!**/.next/**',
  ],

  coverageDirectory: '<rootDir>/coverage',
  coverageReporters: ['text', 'lcov', 'html', 'json'],
  
  // Coverage thresholds
  coverageThreshold: {
    global: {
      branches: 70,
      functions: 70,
      lines: 70,
      statements: 70,
    },
    // Higher standards for shared packages
    'packages/shared-auth/src/**/*.ts': {
      branches: 85,
      functions: 85,
      lines: 85,
      statements: 85,
    },
    'packages/shared-types/src/**/*.ts': {
      branches: 90,
      functions: 90,
      lines: 90,
      statements: 90,
    },
  },

  // Test environment
  testEnvironment: 'jsdom',
  
  // Setup files
  setupFilesAfterEnv: ['<rootDir>/jest.setup.js'],
  
  // Module name mapping for workspace packages
  moduleNameMapping: {
    '^@tap2go/shared-auth$': '<rootDir>/packages/shared-auth/src',
    '^@tap2go/shared-types$': '<rootDir>/packages/shared-types/src',
    '^@tap2go/shared-ui$': '<rootDir>/packages/shared-ui/src',
    '^@tap2go/shared-utils$': '<rootDir>/packages/shared-utils/src',
    '^@tap2go/database$': '<rootDir>/packages/database/src',
    '^@tap2go/firebase-config$': '<rootDir>/packages/firebase-config/src',
    '^@tap2go/api-client$': '<rootDir>/packages/api-client/src',
    '^@tap2go/business-logic$': '<rootDir>/packages/business-logic/src',
    '^@tap2go/config$': '<rootDir>/packages/config/src',
  },

  // Transform configuration
  transform: {
    '^.+\\.(ts|tsx)$': ['ts-jest', {
      useESM: true,
      tsconfig: {
        jsx: 'react-jsx',
      },
    }],
  },

  // File extensions
  moduleFileExtensions: ['ts', 'tsx', 'js', 'jsx', 'json'],
  
  // Test match patterns
  testMatch: [
    '<rootDir>/packages/*/src/**/__tests__/**/*.{ts,tsx}',
    '<rootDir>/packages/*/src/**/*.{test,spec}.{ts,tsx}',
    '<rootDir>/apps/*/src/**/__tests__/**/*.{ts,tsx}',
    '<rootDir>/apps/*/src/**/*.{test,spec}.{ts,tsx}',
  ],

  // Ignore patterns
  testPathIgnorePatterns: [
    '<rootDir>/node_modules/',
    '<rootDir>/.next/',
    '<rootDir>/dist/',
    '<rootDir>/build/',
  ],

  // Watch plugins
  watchPlugins: [
    'jest-watch-typeahead/filename',
    'jest-watch-typeahead/testname',
  ],
};
