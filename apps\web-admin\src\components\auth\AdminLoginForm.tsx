'use client';

/**
 * Admin Login Form
 * Professional admin login form using shared components with admin-specific styling
 */

import React from 'react';
import { useRouter } from 'next/navigation';
import { useAdminAuth } from '@/contexts/AuthContext';
import {
  AuthFormLayout,
  AuthFormField,
  AuthFormButton,
  AuthErrorAlert,
  useAuthForm,
  type AuthFormConfig,
} from 'shared-ui';
import {
  UserIcon,
  KeyIcon,
  ShieldCheckIcon,
} from '@heroicons/react/24/outline';

interface AdminLoginFormProps {
  onSwitchToSignup?: () => void;
}

export default function AdminLoginForm({ onSwitchToSignup }: AdminLoginFormProps) {
  const router = useRouter();
  const { signIn, loading, authError, clearError } = useAdminAuth();

  // Form configuration
  const formConfig: AuthFormConfig = {
    fields: [
      {
        name: 'email',
        label: 'Email Address',
        type: 'email',
        placeholder: 'Enter your admin email',
        autoComplete: 'email',
        validation: {
          required: true,
          email: true,
        },
      },
      {
        name: 'password',
        label: 'Password',
        type: 'password',
        placeholder: 'Enter your password',
        autoComplete: 'current-password',
        validation: {
          required: true,
          password: {
            minLength: 6,
          },
        },
      },
    ],
    submitButtonText: 'Sign In to Admin Panel',
    loadingText: 'Signing in...',
  };

  const {
    formData,
    errors,
    isSubmitting,
    handleInputChange,
    handleSubmit,
  } = useAuthForm(formConfig);

  // Handle form submission
  const onSubmit = async (data: { [key: string]: string }) => {
    try {
      await signIn(data.email, data.password);
      // Redirect will be handled by auth context or route protection
      router.push('/dashboard');
    } catch (error) {
      // Error is handled by auth context
      console.error('Admin login error:', error);
    }
  };

  // Clear auth error when form data changes
  React.useEffect(() => {
    if (authError) {
      clearError();
    }
  }, [formData, authError, clearError]);

  return (
    <AuthFormLayout
      title="Admin Sign In"
      subtitle="Access your Tap2Go admin dashboard"
      icon={<ShieldCheckIcon />}
      theme="admin"
      footer={
        onSwitchToSignup && (
          <p className="text-sm text-gray-600">
            Need an admin account?{' '}
            <button
              type="button"
              onClick={onSwitchToSignup}
              className="font-medium text-blue-600 hover:text-blue-500 transition-colors"
            >
              Contact your administrator
            </button>
          </p>
        )
      }
    >
      <form onSubmit={(e) => handleSubmit(e, onSubmit)} className="space-y-6">
        {/* Global Error Message */}
        <AuthErrorAlert
          error={authError}
          title="Sign In Failed"
          theme="admin"
          onDismiss={clearError}
        />

        {/* Email Field */}
        <AuthFormField
          id="email"
          name="email"
          type="email"
          label="Email Address"
          placeholder="Enter your admin email"
          value={formData.email || ''}
          error={errors.email}
          required
          autoComplete="email"
          icon={<UserIcon />}
          theme="admin"
          onChange={handleInputChange}
        />

        {/* Password Field */}
        <AuthFormField
          id="password"
          name="password"
          type="password"
          label="Password"
          placeholder="Enter your password"
          value={formData.password || ''}
          error={errors.password}
          required
          autoComplete="current-password"
          icon={<KeyIcon />}
          theme="admin"
          onChange={handleInputChange}
        />

        {/* Submit Button */}
        <AuthFormButton
          type="submit"
          variant="primary"
          size="lg"
          theme="admin"
          loading={loading || isSubmitting}
          disabled={loading || isSubmitting}
          fullWidth
          loadingText="Signing in..."
        >
          Sign In to Admin Panel
        </AuthFormButton>

        {/* Additional Admin Info */}
        <div className="mt-6 p-4 bg-blue-50 border border-blue-200 rounded-md">
          <div className="flex">
            <div className="flex-shrink-0">
              <ShieldCheckIcon className="h-5 w-5 text-blue-400" />
            </div>
            <div className="ml-3">
              <h3 className="text-sm font-medium text-blue-800">
                Admin Access Only
              </h3>
              <div className="mt-2 text-sm text-blue-700">
                <p>
                  This portal is restricted to authorized administrators only. 
                  All access attempts are logged and monitored.
                </p>
              </div>
            </div>
          </div>
        </div>
      </form>
    </AuthFormLayout>
  );
}
