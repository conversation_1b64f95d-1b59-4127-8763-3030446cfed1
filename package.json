{"name": "tap2go-monorepo", "version": "0.1.0", "private": true, "workspaces": ["apps/*", "packages/*"], "scripts": {"build": "turbo run build", "build:packages": "turbo run build --filter=\"./packages/*\"", "build:apps": "turbo run build --filter=\"./apps/*\"", "build:parallel": "turbo run build --parallel", "dev": "turbo run dev", "lint": "turbo run lint", "lint:fix": "turbo run lint -- --fix", "clean": "turbo run clean", "sync-env": "node scripts/sync-env.js", "postinstall": "node scripts/sync-env.js", "type-check": "turbo run type-check", "type-check:packages": "turbo run type-check --filter=\"./packages/*\"", "type-check:apps": "turbo run type-check --filter=\"./apps/*\"", "type-check:web": "turbo run type-check --filter=web", "type-check:web-driver": "turbo run type-check --filter=web-driver", "type-check:web-admin": "turbo run type-check --filter=web-admin", "type-check:mobile-customer": "turbo run type-check --filter=mobile-customer", "web:dev": "turbo run dev --filter=web", "web:build": "turbo run build --filter=web", "web:start": "cd apps/web && pnpm start", "web:deploy": "cd apps/web && pnpm run build", "web-driver:dev": "turbo run dev --filter=web-driver", "web-driver:build": "pnpm exec turbo run build --filter=web-driver", "web-driver:start": "cd apps/web-driver && pnpm start", "web-driver:deploy": "cd apps/web-driver && pnpm run build", "web-admin:dev": "turbo run dev --filter=web-admin", "web-admin:build": "turbo run build --filter=web-admin", "web-admin:start": "cd apps/web-admin && pnpm start", "web-admin:deploy": "cd apps/web-admin && pnpm run build", "web-vendor:dev": "turbo run dev --filter=web-vendor", "web-vendor:build": "turbo run build --filter=web-vendor", "web-vendor:start": "cd apps/web-vendor && pnpm start", "web-vendor:deploy": "cd apps/web-vendor && pnpm run build", "mobile:dev": "pnpm run sync-env && turbo run dev --filter=mobile-customer", "mobile:build": "turbo run build --filter=mobile-customer", "mobile:ios": "turbo run ios --filter=mobile-customer", "mobile:android": "turbo run android --filter=mobile-customer", "vercel-build": "cd apps/web && pnpm run build", "start": "cd apps/web && pnpm start"}, "devDependencies": {"@turbo/gen": "^2.0.0", "next": "15.3.2", "turbo": "^2.0.0", "typescript": "5.8.3"}, "dependencies": {"@babel/runtime": "^7.27.6", "@headlessui/react": "^2.2.4", "color": "^4.2.3", "color-convert": "^3.1.0", "color-name": "^2.0.0", "color-string": "^1.9.1", "hoist-non-react-statics": "^3.3.2", "nativewind": "2.0.11", "react": "19.0.0", "react-dom": "19.0.0", "redux-persist": "^6.0.0", "scheduler": "^0.25.0", "simple-swizzle": "^0.2.2"}, "packageManager": "pnpm@8.15.6", "pnpm": {"shamefullyHoist": true, "hoistPattern": ["*"], "publicHoistPattern": ["*expo*", "*react-native*", "*@babel*", "*metro*", "*expo-modules-core*", "scheduler", "color-string", "color", "color-convert", "color-name", "simple-swizzle", "hoist-non-react-statics", "@babel/runtime"], "overrides": {"@react-native-async-storage/async-storage": "2.1.2", "@types/react": "~19.0.10", "babel-preset-expo": "~13.0.0"}, "peerDependencyRules": {"allowedVersions": {"react": "19.x"}, "ignoreMissing": ["@react-native-async-storage/async-storage"]}}, "engines": {"node": "18.x || 20.x || 22.x", "npm": ">=8.0.0", "pnpm": ">=8.0.0"}}