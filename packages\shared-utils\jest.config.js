/**
 * Jest Configuration for @tap2go/shared-utils
 */

module.exports = {
  displayName: '@tap2go/shared-utils',
  preset: 'ts-jest',
  testEnvironment: 'node',
  
  rootDir: '.',
  setupFilesAfterEnv: ['<rootDir>/../../jest.setup.js'],
  
  testMatch: [
    '<rootDir>/src/**/__tests__/**/*.{ts,tsx}',
    '<rootDir>/src/**/*.{test,spec}.{ts,tsx}',
  ],
  
  collectCoverageFrom: [
    'src/**/*.{ts,tsx}',
    '!src/**/*.d.ts',
    '!src/index.ts',
    '!src/**/index.ts',
  ],
  
  transform: {
    '^.+\\.(ts|tsx)$': ['ts-jest', { useESM: true }],
  },
  
  moduleFileExtensions: ['ts', 'tsx', 'js', 'jsx', 'json'],
  testPathIgnorePatterns: ['<rootDir>/node_modules/', '<rootDir>/dist/'],
};
