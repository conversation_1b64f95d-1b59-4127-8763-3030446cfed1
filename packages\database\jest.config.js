/**
 * Jest Configuration for @tap2go/database
 */

module.exports = {
  displayName: '@tap2go/database',
  preset: 'ts-jest',
  testEnvironment: 'node',
  
  rootDir: '.',
  setupFilesAfterEnv: ['<rootDir>/../../jest.setup.js'],
  
  testMatch: [
    '<rootDir>/src/**/__tests__/**/*.{ts,tsx}',
    '<rootDir>/src/**/*.{test,spec}.{ts,tsx}',
  ],
  
  collectCoverageFrom: [
    'src/**/*.{ts,tsx}',
    '!src/**/*.d.ts',
    '!src/index.ts',
    '!src/**/index.ts',
  ],
  
  moduleNameMapping: {
    '^@tap2go/shared-types$': '<rootDir>/../shared-types/src',
    '^@tap2go/firebase-config$': '<rootDir>/../firebase-config/src',
  },
  
  transform: {
    '^.+\\.(ts|tsx)$': ['ts-jest', { useESM: true }],
  },
  
  moduleFileExtensions: ['ts', 'tsx', 'js', 'jsx', 'json'],
  testPathIgnorePatterns: ['<rootDir>/node_modules/', '<rootDir>/dist/'],
};
