{"name": "web-admin", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack --port 3004", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"react": "^19.0.0", "react-dom": "^19.0.0", "next": "15.3.5", "@heroicons/react": "^2.2.0", "@tap2go/shared-auth": "workspace:*", "@tap2go/shared-ui": "workspace:*", "@tap2go/shared-types": "workspace:*", "@tap2go/firebase-config": "workspace:*", "@tap2go/database": "workspace:*"}, "devDependencies": {"typescript": "^5", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "@tailwindcss/postcss": "^4", "tailwindcss": "^4", "eslint": "^9", "eslint-config-next": "15.3.5", "@eslint/eslintrc": "^3"}}