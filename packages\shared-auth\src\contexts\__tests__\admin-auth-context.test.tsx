/**
 * AdminAuthContext Tests
 * Tests for the admin authentication context provider
 */

import React from 'react';
import { render, screen, waitFor, act } from '@testing-library/react';
import { AdminAuthProvider, useAdminAuth } from '../admin-auth-context';

// Mock the auth service
jest.mock('../services/auth-service');
jest.mock('../services/user-database');

// Test component that uses the auth context
const TestComponent = () => {
  const { user, loading, signIn, signOut, authError } = useAdminAuth();
  
  return (
    <div>
      <div data-testid="loading">{loading ? 'loading' : 'not-loading'}</div>
      <div data-testid="user">{user ? user.email : 'no-user'}</div>
      <div data-testid="error">{authError || 'no-error'}</div>
      <button onClick={() => signIn('<EMAIL>', 'password')}>
        Sign In
      </button>
      <button onClick={signOut}>Sign Out</button>
    </div>
  );
};

describe('AdminAuthContext', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should provide initial loading state', () => {
    render(
      <AdminAuthProvider>
        <TestComponent />
      </AdminAuthProvider>
    );

    expect(screen.getByTestId('loading')).toHaveTextContent('loading');
    expect(screen.getByTestId('user')).toHaveTextContent('no-user');
    expect(screen.getByTestId('error')).toHaveTextContent('no-error');
  });

  it('should handle sign in', async () => {
    render(
      <AdminAuthProvider>
        <TestComponent />
      </AdminAuthProvider>
    );

    const signInButton = screen.getByText('Sign In');
    
    await act(async () => {
      signInButton.click();
    });

    // Wait for loading to complete
    await waitFor(() => {
      expect(screen.getByTestId('loading')).toHaveTextContent('not-loading');
    });
  });

  it('should handle sign out', async () => {
    render(
      <AdminAuthProvider>
        <TestComponent />
      </AdminAuthProvider>
    );

    const signOutButton = screen.getByText('Sign Out');
    
    await act(async () => {
      signOutButton.click();
    });

    await waitFor(() => {
      expect(screen.getByTestId('user')).toHaveTextContent('no-user');
    });
  });

  it('should handle authentication errors', async () => {
    // Mock auth service to throw error
    const mockAuthService = {
      signIn: jest.fn().mockRejectedValue(new Error('Invalid credentials')),
    };

    render(
      <AdminAuthProvider>
        <TestComponent />
      </AdminAuthProvider>
    );

    const signInButton = screen.getByText('Sign In');
    
    await act(async () => {
      signInButton.click();
    });

    await waitFor(() => {
      expect(screen.getByTestId('loading')).toHaveTextContent('not-loading');
    });
  });

  it('should throw error when used outside provider', () => {
    // Suppress console.error for this test
    const consoleSpy = jest.spyOn(console, 'error').mockImplementation();

    expect(() => {
      render(<TestComponent />);
    }).toThrow('useAdminAuth must be used within an AdminAuthProvider');

    consoleSpy.mockRestore();
  });
});
