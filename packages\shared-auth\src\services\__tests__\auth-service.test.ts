/**
 * AuthService Tests
 * Comprehensive tests for the core authentication service
 */

import { AuthService } from '../auth-service';
import { signInWithEmailAndPassword, createUserWithEmailAndPassword } from 'firebase/auth';

// Mock Firebase Auth
jest.mock('firebase/auth');
jest.mock('@tap2go/firebase-config');

const mockSignInWithEmailAndPassword = signInWithEmailAndPassword as jest.MockedFunction<typeof signInWithEmailAndPassword>;
const mockCreateUserWithEmailAndPassword = createUserWithEmailAndPassword as jest.MockedFunction<typeof createUserWithEmailAndPassword>;

describe('AuthService', () => {
  let authService: AuthService;
  
  beforeEach(() => {
    authService = new AuthService({
      role: 'customer',
      enableGoogleAuth: true,
      enableMultiTabSync: true,
      tokenRefreshInterval: 50 * 60 * 1000,
    });
    
    // Clear all mocks
    jest.clearAllMocks();
  });

  describe('signIn', () => {
    it('should sign in user with valid credentials', async () => {
      const mockUser = {
        uid: 'test-uid',
        email: '<EMAIL>',
        emailVerified: true,
      };
      
      mockSignInWithEmailAndPassword.mockResolvedValue({
        user: mockUser,
      } as any);

      const result = await authService.signIn('<EMAIL>', 'password123');
      
      expect(mockSignInWithEmailAndPassword).toHaveBeenCalledWith(
        expect.any(Object),
        '<EMAIL>',
        'password123'
      );
      expect(result).toEqual(mockUser);
    });

    it('should handle invalid credentials', async () => {
      mockSignInWithEmailAndPassword.mockRejectedValue(
        new Error('auth/invalid-credential')
      );

      await expect(
        authService.signIn('<EMAIL>', 'wrongpassword')
      ).rejects.toThrow();
    });

    it('should handle network errors', async () => {
      mockSignInWithEmailAndPassword.mockRejectedValue(
        new Error('auth/network-request-failed')
      );

      await expect(
        authService.signIn('<EMAIL>', 'password123')
      ).rejects.toThrow();
    });
  });

  describe('signUp', () => {
    it('should create user with valid data', async () => {
      const mockUser = {
        uid: 'test-uid',
        email: '<EMAIL>',
        emailVerified: false,
      };
      
      mockCreateUserWithEmailAndPassword.mockResolvedValue({
        user: mockUser,
      } as any);

      const result = await authService.signUp('<EMAIL>', 'password123');
      
      expect(mockCreateUserWithEmailAndPassword).toHaveBeenCalledWith(
        expect.any(Object),
        '<EMAIL>',
        'password123'
      );
      expect(result).toEqual(mockUser);
    });

    it('should handle email already in use', async () => {
      mockCreateUserWithEmailAndPassword.mockRejectedValue(
        new Error('auth/email-already-in-use')
      );

      await expect(
        authService.signUp('<EMAIL>', 'password123')
      ).rejects.toThrow();
    });

    it('should handle weak password', async () => {
      mockCreateUserWithEmailAndPassword.mockRejectedValue(
        new Error('auth/weak-password')
      );

      await expect(
        authService.signUp('<EMAIL>', '123')
      ).rejects.toThrow();
    });
  });

  describe('configuration', () => {
    it('should initialize with correct role', () => {
      const driverService = new AuthService({
        role: 'driver',
        enableGoogleAuth: false,
      });
      
      expect(driverService).toBeDefined();
    });

    it('should handle Google auth configuration', () => {
      const serviceWithGoogle = new AuthService({
        role: 'customer',
        enableGoogleAuth: true,
      });
      
      const serviceWithoutGoogle = new AuthService({
        role: 'driver',
        enableGoogleAuth: false,
      });
      
      expect(serviceWithGoogle).toBeDefined();
      expect(serviceWithoutGoogle).toBeDefined();
    });
  });
});
