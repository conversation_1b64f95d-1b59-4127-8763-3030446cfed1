{"name": "@tap2go/business-logic", "version": "0.1.0", "private": true, "main": "./src/index.ts", "types": "./src/index.ts", "scripts": {"build": "tsc", "dev": "tsc --watch", "lint": "eslint src/", "type-check": "tsc --noEmit", "clean": "rm -rf dist"}, "dependencies": {"shared-types": "workspace:*", "shared-utils": "workspace:*", "api-client": "workspace:*", "typescript": "^5.8.3"}, "devDependencies": {}}