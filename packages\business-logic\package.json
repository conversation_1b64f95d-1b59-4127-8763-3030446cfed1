{"name": "@tap2go/business-logic", "version": "0.1.0", "private": true, "main": "./src/index.ts", "types": "./src/index.ts", "scripts": {"build": "tsc", "dev": "tsc --watch", "lint": "eslint src/", "type-check": "tsc --noEmit", "clean": "rm -rf dist"}, "dependencies": {"@tap2go/shared-types": "workspace:*", "@tap2go/shared-utils": "workspace:*", "@tap2go/api-client": "workspace:*", "typescript": "^5.8.3"}, "devDependencies": {}}