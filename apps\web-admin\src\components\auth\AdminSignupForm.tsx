'use client';

/**
 * Admin Signup Form
 * Note: In production, admin accounts are typically created by other admins
 * This form is provided for development/initial setup purposes
 */

import React from 'react';
import { useAdminAuth } from '@/contexts/AuthContext';
import {
  AuthFormLayout,
  AuthFormField,
  AuthFormButton,
  AuthErrorAlert,
  useAuthForm,
  type AuthFormConfig,
} from 'shared-ui';
import {
  UserIcon,
  KeyIcon,
  ShieldCheckIcon,
  EnvelopeIcon,
  ExclamationTriangleIcon,
} from '@heroicons/react/24/outline';

interface AdminSignupFormProps {
  onSwitchToLogin?: () => void;
}

export default function AdminSignupForm({ onSwitchToLogin }: AdminSignupFormProps) {
  const { loading, authError, clearError } = useAdminAuth();

  // Form configuration
  const formConfig: AuthFormConfig = {
    fields: [
      {
        name: 'firstName',
        label: 'First Name',
        type: 'text',
        placeholder: 'Enter your first name',
        autoComplete: 'given-name',
        validation: {
          required: true,
          name: {
            minLength: 2,
            maxLength: 50,
            allowSpaces: false,
          },
        },
      },
      {
        name: 'lastName',
        label: 'Last Name',
        type: 'text',
        placeholder: 'Enter your last name',
        autoComplete: 'family-name',
        validation: {
          required: true,
          name: {
            minLength: 2,
            maxLength: 50,
            allowSpaces: false,
          },
        },
      },
      {
        name: 'email',
        label: 'Email Address',
        type: 'email',
        placeholder: 'Enter your admin email',
        autoComplete: 'email',
        validation: {
          required: true,
          email: true,
        },
      },
      {
        name: 'password',
        label: 'Password',
        type: 'password',
        placeholder: 'Create a strong password',
        autoComplete: 'new-password',
        validation: {
          required: true,
          password: {
            minLength: 8,
            requireUppercase: true,
            requireLowercase: true,
            requireNumber: true,
          },
        },
      },
      {
        name: 'confirmPassword',
        label: 'Confirm Password',
        type: 'password',
        placeholder: 'Confirm your password',
        autoComplete: 'new-password',
        validation: {
          required: true,
        },
      },
    ],
    submitButtonText: 'Create Admin Account',
    loadingText: 'Creating account...',
  };

  const {
    formData,
    errors,
    isSubmitting,
    handleInputChange,
    handleSubmit,
    setFieldError,
  } = useAuthForm(formConfig);

  // Handle form submission
  const onSubmit = async (data: { [key: string]: string }) => {
    // Validate password confirmation
    if (data.password !== data.confirmPassword) {
      setFieldError('confirmPassword', 'Passwords do not match');
      return;
    }

    try {
      // Note: In a real application, admin creation would be handled differently
      // This is just for demonstration purposes
      console.log('Admin signup attempt:', {
        firstName: data.firstName,
        lastName: data.lastName,
        email: data.email,
      });
      
      // For now, just redirect to login
      alert('Admin account creation is restricted. Please contact your system administrator.');
      if (onSwitchToLogin) {
        onSwitchToLogin();
      }
    } catch (error) {
      console.error('Admin signup error:', error);
    }
  };

  // Clear auth error when form data changes
  React.useEffect(() => {
    if (authError) {
      clearError();
    }
  }, [formData, authError, clearError]);

  return (
    <AuthFormLayout
      title="Admin Account Setup"
      subtitle="Create your Tap2Go admin account"
      icon={<ShieldCheckIcon />}
      theme="admin"
      footer={
        onSwitchToLogin && (
          <p className="text-sm text-gray-600">
            Already have an admin account?{' '}
            <button
              type="button"
              onClick={onSwitchToLogin}
              className="font-medium text-blue-600 hover:text-blue-500 transition-colors"
            >
              Sign in here
            </button>
          </p>
        )
      }
    >
      <form onSubmit={(e) => handleSubmit(e, onSubmit)} className="space-y-6">
        {/* Development Warning */}
        <div className="bg-yellow-50 border border-yellow-200 rounded-md p-4">
          <div className="flex">
            <div className="flex-shrink-0">
              <ExclamationTriangleIcon className="h-5 w-5 text-yellow-400" />
            </div>
            <div className="ml-3">
              <h3 className="text-sm font-medium text-yellow-800">
                Development Mode
              </h3>
              <div className="mt-2 text-sm text-yellow-700">
                <p>
                  In production, admin accounts are created by existing administrators. 
                  This form is for development purposes only.
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Global Error Message */}
        <AuthErrorAlert
          error={authError}
          title="Account Creation Failed"
          theme="admin"
          onDismiss={clearError}
        />

        {/* Name Fields Row */}
        <div className="grid grid-cols-1 gap-6 sm:grid-cols-2">
          {/* First Name Field */}
          <AuthFormField
            id="firstName"
            name="firstName"
            type="text"
            label="First Name"
            placeholder="Enter your first name"
            value={formData.firstName || ''}
            error={errors.firstName}
            required
            autoComplete="given-name"
            icon={<UserIcon />}
            theme="admin"
            onChange={handleInputChange}
          />

          {/* Last Name Field */}
          <AuthFormField
            id="lastName"
            name="lastName"
            type="text"
            label="Last Name"
            placeholder="Enter your last name"
            value={formData.lastName || ''}
            error={errors.lastName}
            required
            autoComplete="family-name"
            icon={<UserIcon />}
            theme="admin"
            onChange={handleInputChange}
          />
        </div>

        {/* Email Field */}
        <AuthFormField
          id="email"
          name="email"
          type="email"
          label="Email Address"
          placeholder="Enter your admin email"
          value={formData.email || ''}
          error={errors.email}
          required
          autoComplete="email"
          icon={<EnvelopeIcon />}
          theme="admin"
          onChange={handleInputChange}
        />

        {/* Password Field */}
        <AuthFormField
          id="password"
          name="password"
          type="password"
          label="Password"
          placeholder="Create a strong password"
          value={formData.password || ''}
          error={errors.password}
          required
          autoComplete="new-password"
          icon={<KeyIcon />}
          theme="admin"
          onChange={handleInputChange}
        />

        {/* Confirm Password Field */}
        <AuthFormField
          id="confirmPassword"
          name="confirmPassword"
          type="password"
          label="Confirm Password"
          placeholder="Confirm your password"
          value={formData.confirmPassword || ''}
          error={errors.confirmPassword}
          required
          autoComplete="new-password"
          icon={<KeyIcon />}
          theme="admin"
          onChange={handleInputChange}
        />

        {/* Submit Button */}
        <AuthFormButton
          type="submit"
          variant="primary"
          size="lg"
          theme="admin"
          loading={loading || isSubmitting}
          disabled={loading || isSubmitting}
          fullWidth
          loadingText="Creating account..."
        >
          Request Admin Account
        </AuthFormButton>
      </form>
    </AuthFormLayout>
  );
}
