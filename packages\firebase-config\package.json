{"name": "@tap2go/firebase-config", "version": "0.1.0", "private": true, "main": "./dist/index.js", "types": "./dist/index.d.ts", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}}, "scripts": {"build": "tsc", "dev": "tsc --watch", "lint": "eslint src/", "type-check": "tsc --noEmit", "clean": "<PERSON><PERSON><PERSON> dist"}, "dependencies": {"firebase": "^11.8.1", "firebase-admin": "^13.4.0", "@tap2go/shared-types": "workspace:*", "typescript": "^5.8.3", "@types/node": "^20.17.57"}, "devDependencies": {"rimraf": "^6.0.1"}}