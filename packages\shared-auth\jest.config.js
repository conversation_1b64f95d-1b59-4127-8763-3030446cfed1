/**
 * Jest Configuration for @tap2go/shared-auth
 */

module.exports = {
  displayName: '@tap2go/shared-auth',
  preset: 'ts-jest',
  testEnvironment: 'jsdom',
  
  // Root directory
  rootDir: '.',
  
  // Setup files
  setupFilesAfterEnv: ['<rootDir>/../../jest.setup.js'],
  
  // Test files
  testMatch: [
    '<rootDir>/src/**/__tests__/**/*.{ts,tsx}',
    '<rootDir>/src/**/*.{test,spec}.{ts,tsx}',
  ],
  
  // Coverage
  collectCoverageFrom: [
    'src/**/*.{ts,tsx}',
    '!src/**/*.d.ts',
    '!src/**/*.stories.{ts,tsx}',
    '!src/index.ts',
    '!src/**/index.ts',
  ],
  
  // Module name mapping
  moduleNameMapping: {
    '^@tap2go/shared-types$': '<rootDir>/../shared-types/src',
    '^@tap2go/firebase-config$': '<rootDir>/../firebase-config/src',
    '^@tap2go/database$': '<rootDir>/../database/src',
  },
  
  // Transform
  transform: {
    '^.+\\.(ts|tsx)$': ['ts-jest', {
      useESM: true,
      tsconfig: {
        jsx: 'react-jsx',
      },
    }],
  },
  
  // Module file extensions
  moduleFileExtensions: ['ts', 'tsx', 'js', 'jsx', 'json'],
  
  // Ignore patterns
  testPathIgnorePatterns: [
    '<rootDir>/node_modules/',
    '<rootDir>/dist/',
  ],
};
